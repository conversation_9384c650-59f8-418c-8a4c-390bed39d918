import { isDev } from "@main/constant";
import { TYPES } from "@main/shared/types";
import logger from "@shared/logger/main-logger";
import type { UpdaterStatus } from "@shared/types/version-updater";
import { app } from "electron";
import {
  type AppUpdater as _AppUpdater,
  autoUpdater,
  type ProgressInfo,
  type UpdateInfo,
} from "electron-updater";
import { inject, injectable } from "inversify";
import {
  CommunicationWay,
  ServiceHandler,
  ServiceRegister,
} from "../shared/reflect";
import { EventNames, sendToRenderer } from "./event-service";
import type { SettingsService } from "./settings-service";

@ServiceRegister(TYPES.UpdaterService)
@injectable()
export class UpdaterService {
  private autoUpdater: _AppUpdater = autoUpdater;
  private status: UpdaterStatus = "idle";
  private initFlag: boolean = false;

  constructor(
    @inject(TYPES.SettingsService) private settingsService: SettingsService,
  ) {
    this.setupEventListeners();
    this.init();
  }

  private updateStatus(
    status: UpdaterStatus,
    options?: { version?: string; precent?: number },
  ): void {
    this.status = status;
    sendToRenderer(EventNames.UPDATER_CHECK_STATUS, {
      status,
      ...options,
    });
  }

  private async init(): Promise<void> {
    this.initFlag = true;

    const autoUpdate = await this.settingsService.getAutoUpdate();
    const feedUrl = await this.settingsService.getFeedUrl();

    this.autoUpdater.autoDownload = isDev ? false : autoUpdate;
    // * Avoid auto install on app quit in dev mode
    this.autoUpdater.autoInstallOnAppQuit = isDev ? false : autoUpdate;
    this.autoUpdater.setFeedURL(feedUrl);
    this.autoUpdater.logger = logger;
    this.autoUpdater.forceDevUpdateConfig = !app.isPackaged;

    if (autoUpdate) {
      this.autoCheckForUpdates();
    }
  }

  private async autoCheckForUpdates(): Promise<void> {
    try {
      this.updateStatus("checking");
      await this.autoUpdater.checkForUpdates();
    } catch (error) {
      logger.error("UpdaterService:checkForUpdates error", { error });
      this.updateStatus("error");
    }
  }

  private setupEventListeners(): void {
    this.autoUpdater.on("error", (error: Error) => {
      logger.error("UpdaterService:error", { error });
      this.updateStatus("error");
    });

    this.autoUpdater.on("update-available", (updateInfo: UpdateInfo) => {
      logger.info("new version available", { updateInfo });
      this.updateStatus("available", { version: updateInfo.version });
    });

    this.autoUpdater.on("update-not-available", (updateInfo: UpdateInfo) => {
      logger.info("no new version available", { updateInfo });
      this.updateStatus("not-available", { version: updateInfo.version });

      if (this.initFlag) {
        this.initFlag = false;
      }
    });

    this.autoUpdater.on("download-progress", (progress: ProgressInfo) => {
      logger.info("downloading", { progress });
      this.updateStatus("downloading", { precent: progress.percent });
    });

    this.autoUpdater.on("update-downloaded", (updateInfo: UpdateInfo) => {
      logger.info("downloaded", { updateInfo });
      this.updateStatus("downloaded");
    });
  }

  @ServiceHandler(CommunicationWay.RENDERER_TO_MAIN__ONE_WAY)
  async setAutoUpdate(
    _event: Electron.IpcMainInvokeEvent,
    autoUpdate: boolean,
  ): Promise<void> {
    this.autoUpdater.autoDownload = autoUpdate;
    this.autoUpdater.autoInstallOnAppQuit = autoUpdate;
    await this.settingsService.setAutoUpdate(autoUpdate);
  }

  @ServiceHandler(CommunicationWay.RENDERER_TO_MAIN__ONE_WAY)
  async checkForUpdates(_event: Electron.IpcMainInvokeEvent): Promise<void> {
    try {
      this.updateStatus("checking");
      await this.autoUpdater.checkForUpdates();
    } catch (error) {
      logger.error("UpdaterService:checkForUpdates error", { error });
      this.updateStatus("error");
    }
  }

  @ServiceHandler(CommunicationWay.RENDERER_TO_MAIN__TWO_WAY)
  async getStatus(_event: Electron.IpcMainInvokeEvent): Promise<UpdaterStatus> {
    return this.status;
  }

  @ServiceHandler(CommunicationWay.RENDERER_TO_MAIN__ONE_WAY)
  async update(_event: Electron.IpcMainInvokeEvent): Promise<void> {
    this.updateStatus("downloading");
    await this.autoUpdater.downloadUpdate();
  }

  @ServiceHandler(CommunicationWay.RENDERER_TO_MAIN__ONE_WAY)
  async install(_event: Electron.IpcMainInvokeEvent): Promise<void> {
    this.autoUpdater.quitAndInstall(true, true);
  }
}
