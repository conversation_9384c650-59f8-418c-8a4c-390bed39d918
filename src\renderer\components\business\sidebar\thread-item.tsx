import { Ldrs<PERSON>oader } from "@renderer/components/business/ldrs-loader";
import { SidebarItem } from "@renderer/components/ui/sidebar";
import { useThreadWithMessages } from "@renderer/queries/hooks/use-threads";
import type { Message, Thread } from "@shared/triplit/types";
import { ThreadMenu } from "./thread-menu";

interface ThreadItemProps {
  id: string;
  thread: Thread;
  isCurrent: boolean;
  onThreadClick: (id: string) => void;
}

export function ThreadItem({
  id,
  isCurrent,
  onThreadClick,
  thread,
}: ThreadItemProps) {
  const { data: threadData } = useThreadWithMessages(thread.id);

  const latestAssistantMessage = threadData?.messages
    ?.filter((message: Message) => message.role === "assistant")
    .sort((a: Message, b: Message) => b.orderSeq - a.orderSeq)[0];

  const pending = latestAssistantMessage?.status === "pending";

  return (
    <SidebarItem
      className="flex h-10 rounded-[10px] px-0 pr-2 pl-3 [&_.text-muted-fg]:text-muted-fg"
      key={id}
      isCurrent={isCurrent}
      onClick={() => onThreadClick(id)}
    >
      {pending && <LdrsLoader type="line-spinner" size={16} stroke={1} />}
      <ThreadMenu thread={thread} />
    </SidebarItem>
  );
}
